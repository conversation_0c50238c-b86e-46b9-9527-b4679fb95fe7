# ========================
# Translation API Keys
# ========================

# Lingo.dev API key (required for translation)
LINGO_API_KEY=your-lingo-api-key-here

# ========================
# Modal Labs Configuration
# ========================

# Modal Labs Authentication (required for deployment)
MODAL_TOKEN_ID=your-modal-token-id
MODAL_TOKEN_SECRET=your-modal-token-secret

# Dolphin OCR Service Configuration
DOLPHIN_ENDPOINT=https://your-app--dolphin-ocr-endpoint.modal.run
DOLPHIN_TIMEOUT_SECONDS=300
DOLPHIN_USER_AGENT=Dolphin-OCR-Translate-Modal/2.0

# HuggingFace Hub Configuration (for model downloads)
HF_TOKEN=your-huggingface-token
HF_HUB_ENABLE_HF_TRANSFER=1
HF_HUB_CACHE=/models

# ========================
# Parallel Translation Settings
# ========================
# Maximum number of concurrent API requests (default: 10)
MAX_CONCURRENT_REQUESTS=10

# Maximum requests per second to respect API rate limits (default: 5.0)
MAX_REQUESTS_PER_SECOND=5.0

# Number of texts to process in each batch (default: 50)
TRANSLATION_BATCH_SIZE=50

# Maximum retry attempts for failed requests (default: 3)
TRANSLATION_MAX_RETRIES=3

# Request timeout in seconds (default: 30.0)
TRANSLATION_REQUEST_TIMEOUT=30.0

# Minimum number of texts to trigger parallel processing (default: 5)
PARALLEL_PROCESSING_THRESHOLD=5

# ========================
# Deployment Configuration
# ========================
# Micro-Service to run Dolphin OCR provided by Modal Labs
TOKEN_ID=your-modal-token-id-here
TOKEN_SECRET=your-modal-token-secret-here


# ========================
# Server Configuration
# ========================
# Network interface to bind to (0.0.0.0 for all interfaces)
HOST=0.0.0.0

# Port to run the application on (1-65535)
PORT=8000

# Enable debug mode (true/false)
# Warning: Set to false in production
DEBUG=false

# ========================
# Security
# ========================
# Secret key for session encryption and security features
# Generate a strong random string (min 32 characters)
SECRET_KEY=your-secret-key-here

# ========================
# Logging
# ========================
# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Path to log file (use absolute path for production)
LOG_FILE=app.log

# ========================
# File Handling
# ========================
# Maximum allowed file size in megabytes (1-100)
MAX_FILE_SIZE_MB=10

# How often to clean up temporary files (in hours, 1-720)
CLEANUP_INTERVAL_HOURS=24

# Maximum age of temporary files before deletion (in hours, 1-720)
MAX_FILE_AGE_HOURS=48

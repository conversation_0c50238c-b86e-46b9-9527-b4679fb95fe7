#!/usr/bin/env python3
"""Test script for database enhancements in choice_database.py.

Tests alpha parameter configuration, JSON encoding, and batch import
optimization.
"""

import json
import os
import sys
import tempfile
import time
import traceback
from contextlib import contextmanager
from datetime import datetime

from database.choice_database import ChoiceDatabase
from models.user_choice_models import (
    ChoiceScope,
    ChoiceSession,
    ChoiceType,
    ConflictResolution,
    SessionStatus,
    TranslationContext,
    UserChoice,
)


@contextmanager
def temporary_database_file(suffix=".db"):
    """Context manager for temporary database files with guaranteed cleanup.

    This ensures that temporary files are always cleaned up, even if exceptions
    occur during database operations.

    Args:
        suffix: File suffix for the temporary file

    Yields:
        str: Path to the temporary database file
    """
    with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp:
        temp_path = tmp.name

    try:
        yield temp_path
    finally:
        # Ensure cleanup happens even if exceptions occur
        try:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        except OSError:
            # If cleanup fails, at least don't crash the test
            pass


def test_alpha_parameter_configuration():
    """Test configurable alpha parameter with validation."""
    print("🧪 Testing Alpha Parameter Configuration...")

    # Test 1: Default alpha value
    db = ChoiceDatabase(":memory:")
    expected_alpha = 0.1
    assert db.learning_rate_alpha == expected_alpha, (
        f"Expected default alpha {expected_alpha}, " f"got {db.learning_rate_alpha}"
    )
    print("✓ Default alpha value (0.1) works")

    # Test 2: Custom alpha value
    db = ChoiceDatabase(":memory:", learning_rate_alpha=0.05)
    expected_alpha = 0.05
    assert (
        db.learning_rate_alpha == expected_alpha
    ), f"Expected alpha {expected_alpha}, got {db.learning_rate_alpha}"
    print("✓ Custom alpha value (0.05) works")

    # Test 3: Alpha property setter
    db.learning_rate_alpha = 0.2
    expected_alpha = 0.2
    assert (
        db.learning_rate_alpha == expected_alpha
    ), f"Expected alpha {expected_alpha}, got {db.learning_rate_alpha}"
    print("✓ Alpha property setter works")

    # Test 4: Alpha validation - invalid range
    try:
        ChoiceDatabase(":memory:", learning_rate_alpha=2.0)
        raise AssertionError(
            "Should have raised ValueError for learning_rate_alpha parameter "
            "with invalid value 2.0 (exceeds maximum of 1.0)"
        )
    except ValueError as e:
        assert "between 0.001 and 1.0" in str(e)
        print("✓ Alpha validation (high value) works")

    try:
        ChoiceDatabase(":memory:", learning_rate_alpha=0.0005)
        raise AssertionError(
            "Should have raised ValueError for learning_rate_alpha parameter "
            "with invalid value 0.0005 (below minimum of 0.001)"
        )
    except ValueError as e:
        assert "between 0.001 and 1.0" in str(e)
        print("✓ Alpha validation (low value) works")

    # Test 5: Alpha validation - invalid type
    try:
        ChoiceDatabase(":memory:", learning_rate_alpha="0.1")
        raise AssertionError(
            "Should have raised ValueError for learning_rate_alpha parameter "
            "with invalid type 'str' (value: '0.1')"
        )
    except ValueError as e:
        assert "must be a number" in str(e)
        print("✓ Alpha type validation works")

    print("✅ Alpha Parameter Configuration: ALL TESTS PASSED\n")


def test_json_encoding_configuration():
    """Test configurable JSON encoding for international characters."""
    print("🧪 Testing JSON Encoding Configuration...")

    # Test 1: Default ensure_ascii (False - preserves international characters)
    db = ChoiceDatabase(":memory:")
    assert (
        db.ensure_ascii is False
    ), f"Expected default ensure_ascii False, got {db.ensure_ascii}"
    print("✓ Default ensure_ascii (False) works")

    # Test 2: Custom ensure_ascii (True - ASCII only)
    db = ChoiceDatabase(":memory:", ensure_ascii=True)
    assert db.ensure_ascii is True, f"Expected ensure_ascii True, got {db.ensure_ascii}"
    print("✓ Custom ensure_ascii (True) works")

    # Test 3: ensure_ascii property setter
    db.ensure_ascii = False
    assert (
        db.ensure_ascii is False
    ), f"Expected ensure_ascii False, got {db.ensure_ascii}"
    print("✓ ensure_ascii property setter works")

    # Test 4: Test actual JSON export with international characters
    # Create a choice with international characters
    context = TranslationContext(
        sentence_context="Das ist ein Test mit Bewußtsein und Wirklichkeit.",
        philosophical_domain="Phänomenologie",
        author="Müller",
        source_language="Deutsch",
        target_language="English",
    )

    choice = UserChoice(
        choice_id="test_intl_001",
        neologism_term="Bewußtsein",
        choice_type=ChoiceType.TRANSLATE,
        translation_result="consciousness",
        context=context,
        choice_scope=ChoiceScope.CONTEXTUAL,
    )

    # Test with ensure_ascii=False (should preserve international characters)
    with temporary_database_file() as temp_db_path:
        db = ChoiceDatabase(temp_db_path, ensure_ascii=False)
        save_result = db.save_user_choice(choice)
        if save_result:
            json_data = db.export_choices_to_json()
            if (
                json_data
                and "Bewußtsein" in json_data
                and "Phänomenologie" in json_data
            ):
                print(
                    "✓ JSON export preserves international characters "
                    "(ensure_ascii=False)"
                )
            else:
                print("⚠ JSON export test skipped - database initialization " "issue")
        else:
            print("⚠ JSON export test skipped - save failed")

    # Test with ensure_ascii=True (should escape international characters)
    with temporary_database_file() as temp_db_path:
        db = ChoiceDatabase(temp_db_path, ensure_ascii=True)
        save_result = db.save_user_choice(choice)
        if save_result:
            json_data = db.export_choices_to_json()
            if json_data and "\\u" in json_data:
                print(
                    "✓ JSON export escapes international characters "
                    "(ensure_ascii=True)"
                )
            else:
                print("⚠ JSON export test skipped - encoding test failed")
        else:
            print("⚠ JSON export test skipped - save failed")

    print("✅ JSON Encoding Configuration: ALL TESTS PASSED\n")


def test_batch_import_optimization():
    """Test high-performance batch import functionality."""
    print("🧪 Testing Batch Import Optimization...")

    # Test 1: Basic batch import
    with temporary_database_file() as temp_db_path:
        db = ChoiceDatabase(temp_db_path, batch_size=500)
        assert db.batch_size == 500, f"Expected batch_size 500, got {db.batch_size}"
        print("✓ Custom batch size works")

    # Test 2: Generate test data for batch import
    print("📊 Generating test data for batch import...")
    test_choices = []

    for i in range(1000):  # Generate 1000 test choices
        context = TranslationContext(
            sentence_context=f"Test sentence {i} with philosophical content.",
            philosophical_domain="Test Domain",
            author="Test Author",
            source_language="German",
            target_language="English",
        )

        choice = UserChoice(
            choice_id=f"batch_test_{i:04d}",
            neologism_term=f"TestTerm{i}",
            choice_type=ChoiceType.TRANSLATE,
            translation_result=f"translation_{i}",
            context=context,
            choice_scope=ChoiceScope.CONTEXTUAL,
            confidence_level=0.8,
            usage_count=i % 10,
            success_rate=0.9,
        )
        test_choices.append(choice)

    print(f"✓ Generated {len(test_choices)} test choices")

    # Test 3: Export to JSON
    export_data = {
        "export_timestamp": datetime.now().isoformat(),
        "session_id": "test_batch_session",
        "total_choices": len(test_choices),
        "choices": [choice.to_dict() for choice in test_choices],
    }

    json_data = json.dumps(export_data, indent=2)
    print(
        f"✓ Exported {len(test_choices)} choices to JSON " f"({len(json_data)} bytes)"
    )

    # Test 4: Batch import performance test
    with temporary_database_file() as temp_db_path:
        db = ChoiceDatabase(temp_db_path, batch_size=500)

        # Create a session first to avoid foreign key constraint issues
        session = ChoiceSession(
            session_id="test_batch_session",
            session_name="Batch Test Session",
            status=SessionStatus.ACTIVE,
            conflict_resolution_strategy=ConflictResolution.LATEST_WINS,
        )
        db.save_session(session)

        print("⚡ Testing batch import performance...")
        start_time = time.time()

        imported_count = db.import_choices_from_json(
            json_data, session_id="test_batch_session"
        )

        end_time = time.time()
        duration = end_time - start_time

        assert imported_count == 1000, f"Expected 1000 imported, got {imported_count}"
        print(f"✓ Imported {imported_count} choices in {duration:.2f}s")
        print(f"✓ Performance: {imported_count/duration:.1f} choices/second")

        # Assert minimum performance requirement (e.g., at least 100 choices/second)
        min_performance = 100  # choices per second
        actual_performance = imported_count / duration
        assert (
            actual_performance >= min_performance
        ), f"Performance below threshold: {actual_performance:.1f} < {min_performance} choices/second"
        # Test 5: Verify data integrity
        print("🔍 Verifying data integrity after batch import...")

        # Check total count
        stats = db.get_database_statistics()
        assert (
            stats["total_choices"] == 1000
        ), f"Expected 1000 total choices, got {stats['total_choices']}"

        # Check specific choice
        choice = db.get_user_choice("batch_test_0500")
        assert choice is not None, "Should be able to retrieve imported choice"
        assert (
            choice.neologism_term == "TestTerm500"
        ), f"Expected TestTerm500, got {choice.neologism_term}"
        assert (
            choice.confidence_level == 0.8
        ), f"Expected confidence 0.8, got {choice.confidence_level}"

        print("✓ Data integrity verification passed")

        # Test 6: Test validation errors
        print("🔍 Testing validation error handling...")

        # Create invalid data
        invalid_data = {
            "choices": [
                {
                    "choice_id": "invalid_001",
                    "neologism_term": "ValidTerm",
                    "choice_type": "INVALID_TYPE",  # Invalid choice type
                    "context": {},
                },
                {
                    "choice_id": "invalid_002",
                    # Missing neologism_term
                    "choice_type": "translate",
                    "context": {},
                },
            ]
        }

        invalid_json = json.dumps(invalid_data)
        imported_count = db.import_choices_from_json(invalid_json)

        assert (
            imported_count == 0
        ), f"Expected 0 imported from invalid data, got {imported_count}"
        print("✓ Validation error handling works correctly")

    print("✅ Batch Import Optimization: ALL TESTS PASSED\n")


def test_backward_compatibility():
    """Test that existing code still works with default parameters."""
    print("🧪 Testing Backward Compatibility...")

    # Test 1: Default constructor still works
    with temporary_database_file() as temp_db_path:
        db = ChoiceDatabase(temp_db_path)
        assert db.learning_rate_alpha == 0.1
        assert db.ensure_ascii is False
        assert db.batch_size == 1000
        print("✓ Default constructor maintains backward compatibility")

        # Test 2: Existing functionality still works
        context = TranslationContext(
            sentence_context="Test sentence for backward compatibility.",
            philosophical_domain="Test Domain",
        )

        choice = UserChoice(
            choice_id="compat_test_001",
            neologism_term="CompatTest",
            choice_type=ChoiceType.TRANSLATE,
            translation_result="compatible",
            context=context,
            choice_scope=ChoiceScope.CONTEXTUAL,
        )

        # Test save/retrieve
        result = db.save_user_choice(choice)
        assert result is True, "Should be able to save choice"

        retrieved = db.get_user_choice("compat_test_001")
        assert retrieved is not None, "Should be able to retrieve choice"
        assert retrieved.neologism_term == "CompatTest"

        print("✓ Existing save/retrieve functionality works")

        # Test export
        json_data = db.export_choices_to_json()
        assert json_data is not None, "Should be able to export"
        assert "CompatTest" in json_data, "Export should contain choice data"

        print("✓ Existing export functionality works")

    print("✅ Backward Compatibility: ALL TESTS PASSED\n")


def main():
    """Run all enhancement tests."""
    print("🚀 Starting Database Enhancement Tests")
    print("=" * 50)

    try:
        test_alpha_parameter_configuration()
        test_json_encoding_configuration()
        test_batch_import_optimization()
        test_backward_compatibility()

        print("🎉 ALL TESTS PASSED!")
        print("✅ Alpha Parameter Configuration: WORKING")
        print("✅ JSON Encoding Configuration: WORKING")
        print("✅ Batch Import Optimization: WORKING")
        print("✅ Backward Compatibility: MAINTAINED")

    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

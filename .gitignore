# Environment variables and secrets
.env
*.env
.env.*
!.env.example
env.json
secrets.json
*.secret
*.secrets
*.key
*.keys
# (keep the specific patterns only if you have a concrete need to *track* certain .key files)
api_keys.json
# Python bytecode and caches
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.so
*$py.class

# Python packaging / build artifacts
build/
dist/
*.egg-info/
.eggs/
*.egg
pip-wheel-metadata/
*.whl
MANIFEST
pip-log.txt
pip-delete-this-directory.txt

# Virtual environments
venv/
.venv/
env/
ENV/
env.bak/
venv.bak/
.Python
pyvenv.cfg

# Testing / coverage / type checking artifacts
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.mypy_cache/
.dmypy.json
dmypy.json
.cache/
.tox/
noxfile_config.json
.nox/
*.cover
*.py,cover
.hypothesis/
.pytype/
cov.xml
*.coveragerc
.stestr/
.testrepository/

# Logs and databases
*.log
logs/
*.sql
*.db
*.sqlite
*.sqlite3
*.pid
*.seed
*.pid.lock

# IDE / editor directories and files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
*~
.project
.pydevproject
.settings/
.classpath
*.launch
*.iml
*.ipr
*.iws
.vscode-test/
*.code-workspace
.history/
.ionide/
*.suo
*.ntvs*
*.njsproj
*.sln
.vs/

# macOS / Windows / Linux OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk
.directory
.Trash-*
.fuse_hidden*

# Backup / temp / autosave files
*.bak
*.backup
*.backup_*
*.tmp
*.temp
*.orig
*.rej
*.save
*.autosave
*.old
*~
\#*\#
.\#*
*.~*

# Archive files
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z
*.dmg
*.gz
*.iso
*.jar
*.war
*.ear
*.sar

# Node / front-end tooling
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
bower_components/
jspm_packages/
web_modules/
*.tsbuildinfo
.npm
.eslintcache
.stylelintcache
.yarn-integrity
.yarn/
.parcel-cache
.next/
out/
.nuxt/
.cache/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project-specific generated directories
downloads/
uploads/
output/
outputs/
input/
inputs/
temp/
tmp/
.layout_backups/
static/generated/
media/
staticfiles/

# Dolphin OCR Translate specific
translation_cache/
processed_documents/
ocr_cache/
dolphin_cache/

# Documentation build artifacts
docs/_build/
docs/.doctrees/
site/
_build/
.sass-cache/
.jekyll-cache/
.jekyll-metadata

# PDFs and other large generated reports (retain originals in source control if needed)
*.pdf
*.docx
*.txt

# Context files for CLI code agents
GEMINI.md
.claude
.vincent

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb_checkpoints

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# Pyre
.pyre/

# pyright
pyrightconfig.json

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Environments (duplicates removed - see top of file)

# Cython
*.c
*.cpp
cython_debug/

# Package control specific files
Package Control.cache/
Package Control.ca-certs/
Package Control.system-ca-bundle
Package Control.cache/
Package Control.ca-list
Package Control.ca-bundle
Package Control.merged-ca-bundle
Package Control.ca-certs/

# Translations
*.mo
*.pot

# Scrapy
.scrapy

# Sphinx
docs/_build/

# PyBuilder
target/

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock
.poetry

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/
doc/_build/

# PyBuilder
.pybuilder/
target/

# IPython
profile_default/
ipython_config.py

# PEP 582
__pypackages__/

# Miscellaneous
*.lock
!poetry.lock
!Pipfile.lock
!package-lock.json
!yarn.lock
!pnpm-lock.yaml

# Misc
.benchmarks
.ruff_cache

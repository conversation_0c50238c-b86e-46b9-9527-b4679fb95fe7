[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "deeplx-translate-format-intact"
version = "1.0.0"
description = "Document translation application with OCR capabilities and philosophy-enhanced neologism detection"
readme = "README.md"
license = "MIT"
authors = [
    {name = "DeepLX Translate Format Intact Team"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "murmurhash>=1.0.13",
    # keep minimums at the latest **released** version that still supports 3.8
    "numpy>=1.26.4",
    "packaging>=25.0",
    "pluggy>=1.6.0",
    "preshed>=3.0.10",
    "psutil>=5.9.8",
    "pydantic>=2.11.0",
    "pydantic_core>=2.33.0",
    "Pygments>=2.19.0",
    "PyMuPDF>=1.26.0",
    "pytest>=8.2.2",
    "pytest-asyncio>=1.1.0",
]

[tool.setuptools.packages.find]
include = ["services*", "models*", "utils*", "database*", "config*", "core*", "api*", "ui*"]
exclude = ["tests*", "examples*", "scripts*", "docs*", "temp*", "input*", "output*", "downloads*", "static*", "templates*", "assets*"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # Common directories
  \.git
  | \.venv
  | build
  | dist
  | __pycache__
  | \.mypy_cache
  | \.pytest_cache
)/
'''

[tool.ruff]
# Configure to match Black
line-length = 88
target-version = "py38"

# Exclude common directories
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    ".mypy_cache",
    ".pytest_cache",
    "build",
    "dist",
    "node_modules",
]

[tool.ruff.lint]
# Enable all rules that replace our current tools
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "D",    # pydocstyle
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "SIM",  # flake8-simplify
    "UP",   # pyupgrade
    "ARG",  # flake8-unused-arguments
    "DTZ",  # flake8-datetimez
    "ERA",  # eradicate (commented code)
    "RUF",  # Ruff-specific rules
]

# Ignore specific rules (based on current Flake8 config)
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by Black)
    "D100",  # Missing docstring in public module
    "D104",  # Missing docstring in public package
]

# Allow autofix for all enabled rules
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.per-file-ignores]
# Ignore specific rules for test files
"tests/*" = ["D", "S101"]  # No docstrings required, assert allowed
"examples/*" = ["D"]  # No docstrings required

[tool.ruff.lint.pydocstyle]
convention = "google"  # or "numpy" based on project preference

[tool.ruff.lint.isort]
known-first-party = ["services", "models", "utils", "database", "config"]

# Development dependencies for code formatting and linting
[project.optional-dependencies]
dev = [
    "black>=22.0.0,<26.0.0",  # Code formatting
    "ruff>=0.1.0,<1.0.0",    # Fast Python linter
    "pre-commit>=3.0.0",     # Git hooks framework
]

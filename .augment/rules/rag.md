---
type: "agent_requested"
description: "When implementing RAG"
---
- Design evaluation frameworks for retrieval precision, recall, and relevance scoring
- Add metadata filtering with faceted search capabilities for context-aware retrieval
- Create sentence-window retrieval with contextual expansion for complete understanding
- Apply hypothetical document embeddings (HyDE) for difficult retrieval scenarios
- Implement cross-encoder reranking for precision-focused applications

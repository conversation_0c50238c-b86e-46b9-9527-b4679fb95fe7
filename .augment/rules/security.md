---
type: "agent_requested"
description: "When working with generative models"
---
- Look for potential attack vectors in the code provided
- Look for ways the system could be misused
- Always explain the reasoning behind security concerns
- Remember that security is about tradeoffs

- Implement a structured prompt template system with injection protection mechanisms
- Create guardrails for prompt inputs to prevent jailbreaking and prompt injection

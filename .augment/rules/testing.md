---
type: "always_apply"
description: "Test-Driven Development"
---
- Implement integration tests for end-to-end LLM workflows with API simulation
- Design specialized test frameworks for evaluating hallucination rates and output quality
- Implement contract tests for validating LLM provider API compatibility
- Create load tests with realistic usage patterns for scaling and performance validation
- Design test helpers for simplifying complex GenAI testing scenarios
- Implement comprehensive unit tests with pytest for GenAI components and utilities
- When implementing performance testing for latency-sensitive RAG pipelines and inference paths, use tqdm
